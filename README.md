# REMAN ERP - Enterprise Resource Planning

A comprehensive, responsive ERP (Enterprise Resource Planning) website built with vanilla HTML5, CSS3, and JavaScript, featuring the HCLTech Roobert font family and a professional color scheme.

## Features

### 🎨 Design & UI
- **Professional ERP Interface**: Clean, modern design optimized for business applications
- **HCLTech Roobert Typography**: Custom font family with multiple weights and styles
- **Comprehensive Color Palette**: 
  - Primary: Teal (#2EC0CB), Dark Teal (#23A3AD), <PERSON> (#0F5FDC)
  - Secondary: Various cyan and blue variations
  - Tertiary: Navy (#000032), <PERSON> Gray (#ECF3F8), Black (#000000)
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices

### 📱 Navigation & Functionality
- **Complete Menu Structure**: Records, Edit, Masters, Transactions, Status, Reports, Options, Window, Help
- **Interactive Navigation**: Smooth transitions between sections with active state management
- **Mobile-Friendly**: Collapsible hamburger menu for mobile devices
- **Keyboard Accessibility**: Full keyboard navigation support

### 🔧 Technical Features
- **Vanilla JavaScript**: No external frameworks or dependencies
- **CSS Grid & Flexbox**: Modern layout techniques for responsive design
- **CSS Custom Properties**: Maintainable color system and design tokens
- **Progressive Enhancement**: Works without JavaScript, enhanced with it
- **Print Styles**: Optimized for printing reports and documentation

### ⚡ Interactive Elements
- **Dynamic Content Switching**: Seamless navigation between ERP modules
- **Button Interactions**: Loading states, ripple effects, and feedback
- **Status Dashboard**: Real-time status indicators and metrics
- **User Preferences**: Persistent settings storage using localStorage
- **Notifications**: Toast-style notifications for user feedback

## File Structure

```
REMAN/
├── index.html              # Main HTML structure
├── styles.css              # Complete CSS styling
├── script.js               # JavaScript functionality
├── README.md               # Project documentation
└── HCLTech Roobert_font/   # Font files directory
    └── HCLTech Roobert_font/
        ├── WOFF2/          # Web font files (WOFF2 format)
        ├── WOFF/           # Web font files (WOFF format)
        ├── TTF/            # TrueType font files
        ├── OTF/            # OpenType font files
        └── VF/             # Variable font files
```

## Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Local web server (optional, for font loading)

### Installation
1. Clone or download the project files
2. Ensure all font files are in the correct directory structure
3. Open `index.html` in a web browser, or serve via a local web server

### Running with Local Server
```bash
# Using Python 3
python -m http.server 8000

# Using Node.js (if http-server is installed)
npx http-server

# Using PHP
php -S localhost:8000
```

Then navigate to `http://localhost:8000` in your browser.

## ERP Modules

### 📊 Records Management
- Customer Records
- Product Records  
- Supplier Records
- Employee Records

### ✏️ Edit Operations
- Edit Customer Data
- Edit Product Information
- Edit Pricing

### 🗂️ Master Data
- Item Masters
- Account Masters
- Location Masters
- Currency Masters

### 💼 Transactions
- Sales Orders
- Purchase Orders
- Inventory Transfers
- Financial Entries

### 📈 Status & Monitoring
- System Health Dashboard
- Database Status
- Active Users
- Pending Tasks

### 📋 Reports & Analytics
- Financial Reports
- Sales Analytics
- Inventory Reports
- Custom Reports

### ⚙️ System Configuration
- User Preferences
- System Settings
- Window Management
- Help & Support

## Browser Support

- **Chrome**: 88+
- **Firefox**: 85+
- **Safari**: 14+
- **Edge**: 88+

## Accessibility Features

- **WCAG 2.1 Compliant**: Meets accessibility guidelines
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader Friendly**: Semantic HTML structure
- **High Contrast Support**: Optimized for high contrast mode
- **Reduced Motion**: Respects user motion preferences

## Performance Optimizations

- **Font Preloading**: Critical fonts preloaded for faster rendering
- **CSS Custom Properties**: Efficient styling with minimal redundancy
- **Optimized Images**: No external image dependencies
- **Minimal JavaScript**: Lightweight, efficient code
- **Progressive Enhancement**: Core functionality works without JavaScript

## Customization

### Colors
Modify the CSS custom properties in `styles.css`:
```css
:root {
    --primary-teal: #2EC0CB;
    --primary-blue: #0F5FDC;
    /* Add your custom colors */
}
```

### Typography
Update font references in `styles.css` and ensure font files are properly linked.

### Layout
Modify grid layouts and spacing using the CSS custom properties for consistent design.

## Contributing

1. Follow the existing code style and structure
2. Test across multiple browsers and devices
3. Ensure accessibility compliance
4. Update documentation for any new features

## License

This project is developed for HCLTech and follows their design guidelines and brand standards.

## Version History

- **v1.0.0** - Initial release with complete ERP interface
  - Full navigation system
  - Responsive design
  - HCLTech Roobert font integration
  - Professional color scheme implementation
  - Interactive JavaScript functionality
