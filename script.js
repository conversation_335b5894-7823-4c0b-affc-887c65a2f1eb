/**
 * REMAN ERP - JavaScript Functionality
 * Handles navigation, interactions, and dynamic content
 */

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize the application
 */
function initializeApp() {
    setupNavigation();
    setupMobileMenu();
    setupMastersDropdown();
    setupHeaderInteractions();
    setupNewPartsMaster();
    setupButtonInteractions();
    setupFormInteractions();
    setupAccessibility();

    // Show initial section
    showSection('records');

    console.log('REMAN ERP Application Initialized');
}

/**
 * Setup main navigation functionality
 */
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link:not(#mastersNavLink)');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetSection = this.getAttribute('data-section');

            // Update active nav link
            navLinks.forEach(navLink => navLink.classList.remove('active'));
            this.classList.add('active');

            // Also remove active from Masters nav link
            const mastersNavLink = document.getElementById('mastersNavLink');
            if (mastersNavLink) {
                mastersNavLink.classList.remove('active');
            }

            // Show target section
            showSection(targetSection);

            // Close mobile menu if open
            closeMobileMenu();

            // Hide Masters dropdown if open
            hideMastersDropdown();

            // Smooth scroll to top
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    });
}

/**
 * Setup mobile menu functionality
 */
function setupMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mainNav = document.getElementById('mainNav');

    if (mobileMenuToggle && mainNav) {
        mobileMenuToggle.addEventListener('click', function() {
            toggleMobileMenu();
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileMenuToggle.contains(e.target) && !mainNav.contains(e.target)) {
                closeMobileMenu();
            }
        });

        // Close mobile menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });
    }
}

/**
 * Toggle mobile menu
 */
function toggleMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mainNav = document.getElementById('mainNav');

    mobileMenuToggle.classList.toggle('active');
    mainNav.classList.toggle('active');

    // Update aria-expanded attribute for accessibility
    const isExpanded = mainNav.classList.contains('active');
    mobileMenuToggle.setAttribute('aria-expanded', isExpanded);
}

/**
 * Close mobile menu
 */
function closeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mainNav = document.getElementById('mainNav');

    mobileMenuToggle.classList.remove('active');
    mainNav.classList.remove('active');
    mobileMenuToggle.setAttribute('aria-expanded', 'false');
}

/**
 * Setup Masters dropdown functionality
 */
function setupMastersDropdown() {
    setupMastersHoverInteractions();
    setupMastersSearch();
    setupMastersDropdownItems();
    setupMastersKeyboardNavigation();
}

/**
 * Setup Masters dropdown hover interactions
 */
function setupMastersHoverInteractions() {
    const mastersNavItem = document.getElementById('mastersNavItem');
    const mastersDropdown = document.getElementById('mastersDropdown');
    const mastersNavLink = document.getElementById('mastersNavLink');

    if (mastersNavItem && mastersDropdown) {
        let hoverTimeout;

        // Show dropdown on hover
        mastersNavItem.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
            showMastersDropdown();
        });

        // Hide dropdown with delay when leaving
        mastersNavItem.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(() => {
                hideMastersDropdown();
            }, 200); // 200ms delay
        });

        // Keep dropdown open when hovering over it
        mastersDropdown.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
        });

        mastersDropdown.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(() => {
                hideMastersDropdown();
            }, 200);
        });

        // Handle Masters nav link click
        mastersNavLink.addEventListener('click', function(e) {
            e.preventDefault();

            // On mobile, toggle dropdown
            if (window.innerWidth <= 768) {
                toggleMastersDropdownMobile();
            } else {
                // On desktop, navigate to Masters section
                navigateToMastersSection();
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!mastersNavItem.contains(e.target)) {
                hideMastersDropdown();
            }
        });
    }
}

/**
 * Setup Masters search functionality
 */
function setupMastersSearch() {
    const searchInput = document.getElementById('mastersSearchInput');
    const searchClear = document.getElementById('mastersSearchClear');

    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.trim();
            filterMastersItems(searchTerm);

            // Show/hide clear button
            if (searchClear) {
                searchClear.style.display = searchTerm ? 'flex' : 'none';
            }
        });

        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                clearMastersSearch();
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                focusFirstVisibleItem();
            }
        });
    }

    if (searchClear) {
        searchClear.addEventListener('click', function() {
            clearMastersSearch();
        });
    }
}

/**
 * Setup Masters dropdown item interactions
 */
function setupMastersDropdownItems() {
    const dropdownItems = document.querySelectorAll('.masters-dropdown-item');

    dropdownItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            const targetSubmenu = this.getAttribute('data-submenu');

            // Navigate to Masters section and show specific submenu
            navigateToMastersSection(targetSubmenu);

            // Hide dropdown
            hideMastersDropdown();

            // Close mobile menu if open
            closeMobileMenu();
        });
    });
}

/**
 * Setup keyboard navigation for Masters dropdown
 */
function setupMastersKeyboardNavigation() {
    const mastersDropdown = document.getElementById('mastersDropdown');

    if (mastersDropdown) {
        mastersDropdown.addEventListener('keydown', function(e) {
            const visibleItems = Array.from(mastersDropdown.querySelectorAll('.masters-dropdown-item:not([style*="display: none"])'));
            const currentFocus = document.activeElement;
            const currentIndex = visibleItems.indexOf(currentFocus);

            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    const nextIndex = currentIndex < visibleItems.length - 1 ? currentIndex + 1 : 0;
                    visibleItems[nextIndex]?.focus();
                    break;

                case 'ArrowUp':
                    e.preventDefault();
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : visibleItems.length - 1;
                    visibleItems[prevIndex]?.focus();
                    break;

                case 'Escape':
                    hideMastersDropdown();
                    document.getElementById('mastersNavLink')?.focus();
                    break;

                case 'Enter':
                    if (currentFocus && currentFocus.classList.contains('masters-dropdown-item')) {
                        currentFocus.click();
                    }
                    break;
            }
        });
    }
}

/**
 * Show Masters dropdown
 */
function showMastersDropdown() {
    const mastersDropdown = document.getElementById('mastersDropdown');
    if (mastersDropdown) {
        mastersDropdown.style.opacity = '1';
        mastersDropdown.style.visibility = 'visible';
        mastersDropdown.style.transform = 'translateY(0)';
    }
}

/**
 * Hide Masters dropdown
 */
function hideMastersDropdown() {
    const mastersDropdown = document.getElementById('mastersDropdown');
    if (mastersDropdown) {
        mastersDropdown.style.opacity = '0';
        mastersDropdown.style.visibility = 'hidden';
        mastersDropdown.style.transform = 'translateY(-10px)';
        mastersDropdown.classList.remove('mobile-active');
    }
}

/**
 * Toggle Masters dropdown for mobile
 */
function toggleMastersDropdownMobile() {
    const mastersDropdown = document.getElementById('mastersDropdown');
    if (mastersDropdown) {
        mastersDropdown.classList.toggle('mobile-active');
    }
}

/**
 * Navigate to Masters section with optional submenu
 */
function navigateToMastersSection(submenuId = 'bin-location') {
    // Update active nav link
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(navLink => navLink.classList.remove('active'));

    const mastersNavLink = document.getElementById('mastersNavLink');
    if (mastersNavLink) {
        mastersNavLink.classList.add('active');
    }

    // Show Masters section
    showSection('masters');

    // Show specific submenu content
    showSubmenuContent(submenuId);

    // Smooth scroll to top
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

/**
 * Filter Masters dropdown items based on search term
 */
function filterMastersItems(searchTerm) {
    const dropdownItems = document.querySelectorAll('.masters-dropdown-item');
    const noResults = document.getElementById('mastersNoResults');
    const dropdownGrid = document.getElementById('mastersDropdownGrid');

    let visibleCount = 0;

    dropdownItems.forEach(item => {
        const text = item.querySelector('.masters-item-text').textContent.toLowerCase();
        const matches = text.includes(searchTerm.toLowerCase());

        item.style.display = matches ? 'block' : 'none';
        if (matches) visibleCount++;
    });

    // Show/hide no results message
    if (noResults && dropdownGrid) {
        if (visibleCount === 0 && searchTerm.length > 0) {
            dropdownGrid.style.display = 'none';
            noResults.style.display = 'block';
        } else {
            dropdownGrid.style.display = 'grid';
            noResults.style.display = 'none';
        }
    }
}

/**
 * Clear Masters search
 */
function clearMastersSearch() {
    const searchInput = document.getElementById('mastersSearchInput');
    const searchClear = document.getElementById('mastersSearchClear');

    if (searchInput) {
        searchInput.value = '';
        searchInput.focus();
    }

    if (searchClear) {
        searchClear.style.display = 'none';
    }

    // Reset filter
    filterMastersItems('');
}

/**
 * Focus first visible Masters dropdown item
 */
function focusFirstVisibleItem() {
    const visibleItems = document.querySelectorAll('.masters-dropdown-item:not([style*="display: none"])');
    if (visibleItems.length > 0) {
        visibleItems[0].focus();
    }
}

/**
 * Show specific submenu content and hide others
 */
function showSubmenuContent(submenuId) {
    const submenuContents = document.querySelectorAll('.submenu-content');

    submenuContents.forEach(content => {
        content.classList.remove('active');
    });

    const targetContent = document.getElementById(submenuId);
    if (targetContent) {
        targetContent.classList.add('active');

        // Update page title for submenu
        updateSubmenuPageTitle(submenuId);
    }
}

/**
 * Update page title for submenu
 */
function updateSubmenuPageTitle(submenuId) {
    const submenuTitles = {
        'bin-location': 'Bin Location Master',
        'category': 'Category Master',
        'sac-hsn': 'SAC & HSN / Custom Tariff Master',
        'supplier-order-class': 'Supplier Order Class',
        'new-parts': 'New Parts Master',
        'salvage-parts': 'Salvage Parts Master',
        'core-parts': 'Core Parts Master',
        'exchange-parts': 'Exchange Parts Master',
        'supplier': 'Supplier Master',
        'clearing-agent': 'Clearing Agent Master',
        'dealer': 'Dealer Master',
        'service-technician': 'Service Technician Master',
        'renovator': 'Renovator Master',
        'customer-master': 'Customer Master',
        'operation': 'Operation Master',
        'machining-checklist': 'Machining Checklist',
        'machining-checklist-vendor': 'Machining Checklist Vendor Association'
    };

    const title = submenuTitles[submenuId] || 'Master Data';
    document.title = `${title} - REMAN ERP`;
}

/**
 * Show specific section and hide others
 */
function showSection(sectionId) {
    const sections = document.querySelectorAll('.content-section');

    sections.forEach(section => {
        section.classList.remove('active');
    });

    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');

        // Update page title
        updatePageTitle(sectionId);

        // Trigger section-specific initialization
        initializeSection(sectionId);
    }
}

/**
 * Update page title based on active section
 */
function updatePageTitle(sectionId) {
    const sectionTitles = {
        'records': 'Records Management',
        'edit': 'Edit Operations',
        'masters': 'Master Data',
        'transactions': 'Transactions',
        'status': 'System Status',
        'reports': 'Reports & Analytics',
        'options': 'System Options',
        'window': 'Window Management',
        'help': 'Help & Support'
    };

    const title = sectionTitles[sectionId] || 'REMAN ERP';
    document.title = `${title} - REMAN ERP`;
}

/**
 * Initialize section-specific functionality
 */
function initializeSection(sectionId) {
    switch(sectionId) {
        case 'status':
            updateStatusIndicators();
            break;
        case 'options':
            loadUserPreferences();
            break;
        case 'reports':
            initializeReportFilters();
            break;
        case 'masters':
            initializeMastersSection();
            break;
        default:
            break;
    }
}

/**
 * Initialize Masters section with default submenu
 */
function initializeMastersSection() {
    // Show default submenu content (bin-location)
    showSubmenuContent('bin-location');
}

/**
 * Setup header interactions (search, notifications, profile, logout)
 */
function setupHeaderInteractions() {
    setupSearchFunctionality();
    setupNotificationDropdown();
    setupProfileDropdown();
    setupLogoutFunctionality();
    setupClickOutsideHandlers();
}

/**
 * Setup search functionality
 */
function setupSearchFunctionality() {
    const searchInput = document.getElementById('headerSearch');

    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            const searchTerm = e.target.value.trim();
            if (searchTerm.length > 2) {
                performSearch(searchTerm);
            }
        }, 300));

        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const searchTerm = e.target.value.trim();
                if (searchTerm) {
                    performSearch(searchTerm);
                }
            }
        });
    }
}

/**
 * Perform search operation
 */
function performSearch(searchTerm) {
    console.log(`Searching for: ${searchTerm}`);
    showNotification(`Searching for "${searchTerm}"...`, 'info');

    // Placeholder for actual search implementation
    // This would typically make an API call to search the system
}

/**
 * Setup notification dropdown
 */
function setupNotificationDropdown() {
    const notificationBtn = document.getElementById('notificationBtn');
    const notificationDropdown = document.getElementById('notificationDropdown');
    const markAllReadBtn = document.getElementById('markAllRead');

    if (notificationBtn && notificationDropdown) {
        notificationBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleDropdown(notificationDropdown, notificationBtn);
        });

        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', function() {
                markAllNotificationsAsRead();
            });
        }
    }
}

/**
 * Setup profile dropdown
 */
function setupProfileDropdown() {
    const profileBtn = document.getElementById('profileBtn');
    const profileDropdown = document.getElementById('profileDropdown');

    if (profileBtn && profileDropdown) {
        profileBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleDropdown(profileDropdown, profileBtn);
        });

        // Handle profile menu item clicks
        const profileMenuItems = profileDropdown.querySelectorAll('.profile-menu-item');
        profileMenuItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const action = this.getAttribute('href').substring(1);
                handleProfileMenuAction(action);
                closeAllDropdowns();
            });
        });
    }
}

/**
 * Setup logout functionality
 */
function setupLogoutFunctionality() {
    const logoutBtn = document.getElementById('logoutBtn');

    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            showLogoutConfirmation();
        });
    }
}

/**
 * Setup click outside handlers for dropdowns
 */
function setupClickOutsideHandlers() {
    document.addEventListener('click', function(e) {
        const notificationDropdown = document.getElementById('notificationDropdown');
        const profileDropdown = document.getElementById('profileDropdown');
        const notificationBtn = document.getElementById('notificationBtn');
        const profileBtn = document.getElementById('profileBtn');

        // Close dropdowns if clicking outside
        if (notificationDropdown && !notificationBtn.contains(e.target) && !notificationDropdown.contains(e.target)) {
            closeDropdown(notificationDropdown, notificationBtn);
        }

        if (profileDropdown && !profileBtn.contains(e.target) && !profileDropdown.contains(e.target)) {
            closeDropdown(profileDropdown, profileBtn);
        }
    });

    // Close dropdowns on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllDropdowns();
        }
    });
}

/**
 * Toggle dropdown visibility
 */
function toggleDropdown(dropdown, button) {
    const isActive = dropdown.classList.contains('active');

    // Close all dropdowns first
    closeAllDropdowns();

    if (!isActive) {
        dropdown.classList.add('active');
        button.setAttribute('aria-expanded', 'true');
    }
}

/**
 * Close specific dropdown
 */
function closeDropdown(dropdown, button) {
    dropdown.classList.remove('active');
    button.setAttribute('aria-expanded', 'false');
}

/**
 * Close all dropdowns
 */
function closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('.notification-dropdown, .profile-dropdown');
    const buttons = document.querySelectorAll('.notification-btn, .profile-btn');

    dropdowns.forEach(dropdown => dropdown.classList.remove('active'));
    buttons.forEach(button => button.setAttribute('aria-expanded', 'false'));
}

/**
 * Mark all notifications as read
 */
function markAllNotificationsAsRead() {
    const unreadNotifications = document.querySelectorAll('.notification-item.unread');
    const notificationBadge = document.getElementById('notificationBadge');

    unreadNotifications.forEach(notification => {
        notification.classList.remove('unread');
    });

    if (notificationBadge) {
        notificationBadge.textContent = '0';
        notificationBadge.style.display = 'none';
    }

    showNotification('All notifications marked as read', 'success');
}

/**
 * Handle profile menu actions
 */
function handleProfileMenuAction(action) {
    switch(action) {
        case 'profile':
            showNotification('Opening user profile...', 'info');
            console.log('Navigate to user profile');
            break;
        case 'settings':
            showNotification('Opening settings...', 'info');
            console.log('Navigate to settings');
            break;
        case 'help':
            showNotification('Opening help center...', 'info');
            console.log('Navigate to help');
            break;
        default:
            console.log(`Unknown profile action: ${action}`);
    }
}

/**
 * Show logout confirmation dialog
 */
function showLogoutConfirmation() {
    const confirmed = confirm('Are you sure you want to logout?');

    if (confirmed) {
        performLogout();
    }
}

/**
 * Perform logout operation
 */
function performLogout() {
    showNotification('Logging out...', 'info');

    // Simulate logout process
    setTimeout(() => {
        console.log('User logged out');
        // In a real application, this would redirect to login page
        // window.location.href = '/login';
        showNotification('Logged out successfully', 'success');
    }, 1000);
}

/**
 * Setup New Parts Master functionality
 */
function setupNewPartsMaster() {
    setupNewPartsTabNavigation();
    setupNewPartsFormHandlers();
    setupStockManagement();
    setupImportExportFunctionality();
}

/**
 * Setup tab navigation for New Parts Master
 */
function setupNewPartsTabNavigation() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Update active tab button
            tabButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Update active tab content
            tabContents.forEach(content => content.classList.remove('active'));
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });
}

/**
 * Setup form handlers for New Parts Master
 */
function setupNewPartsFormHandlers() {
    // Main form submission
    const newPartsForm = document.getElementById('newPartsForm');
    if (newPartsForm) {
        newPartsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleNewPartSubmission();
        });
    }

    // Clear form button
    const clearFormBtn = document.getElementById('clearFormBtn');
    if (clearFormBtn) {
        clearFormBtn.addEventListener('click', function() {
            clearNewPartsForm();
        });
    }

    // Preview button
    const previewBtn = document.getElementById('previewBtn');
    if (previewBtn) {
        previewBtn.addEventListener('click', function() {
            previewNewPart();
        });
    }

    // Category and supplier change handlers
    const categorySelect = document.getElementById('categoryName');
    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            updateCategoryCode(this.value);
        });
    }

    const supplierSelect = document.getElementById('supplierName');
    if (supplierSelect) {
        supplierSelect.addEventListener('change', function() {
            updateSupplierCode(this.value);
        });
    }

    // Physical count change handler for stock reconciliation
    const physicalCountInput = document.getElementById('physicalCount');
    if (physicalCountInput) {
        physicalCountInput.addEventListener('input', function() {
            calculateStockVariance();
        });
    }

    // Auto-calculate total stock
    setupStockCalculations();
}

/**
 * Handle new part form submission
 */
function handleNewPartSubmission() {
    const formData = new FormData(document.getElementById('newPartsForm'));
    const partData = Object.fromEntries(formData.entries());

    // Validate required fields
    if (!validateNewPartForm(partData)) {
        return;
    }

    // Show loading state
    const submitBtn = document.querySelector('#newPartsForm button[type="submit"]');
    addLoadingState(submitBtn);

    // Simulate API call
    setTimeout(() => {
        removeLoadingState(submitBtn);
        showNotification('New part saved successfully!', 'success');
        console.log('Part data saved:', partData);

        // Optionally clear form or redirect
        // clearNewPartsForm();
    }, 1500);
}

/**
 * Validate new part form
 */
function validateNewPartForm(data) {
    const requiredFields = ['hsnCode', 'supplierName', 'categoryName', 'partNumber', 'partName'];
    const missingFields = [];

    requiredFields.forEach(field => {
        if (!data[field] || data[field].trim() === '') {
            missingFields.push(field);
        }
    });

    if (missingFields.length > 0) {
        showNotification(`Please fill in all required fields: ${missingFields.join(', ')}`, 'error');
        return false;
    }

    return true;
}

/**
 * Clear new parts form
 */
function clearNewPartsForm() {
    const form = document.getElementById('newPartsForm');
    if (form) {
        form.reset();
        showNotification('Form cleared', 'info');
    }
}

/**
 * Preview new part data
 */
function previewNewPart() {
    const formData = new FormData(document.getElementById('newPartsForm'));
    const partData = Object.fromEntries(formData.entries());

    // Create preview modal or display
    console.log('Part preview:', partData);
    showNotification('Preview functionality - check console for data', 'info');
}

/**
 * Update category code based on selection
 */
function updateCategoryCode(categoryName) {
    const categoryCodeInput = document.getElementById('categoryCode');
    if (categoryCodeInput) {
        const codes = {
            'ENGINE PARTS': 'ENG001',
            'HYDRAULIC PARTS': 'HYD001',
            'ELECTRICAL PARTS': 'ELE001',
            'TRANSMISSION PARTS': 'TRA001'
        };
        categoryCodeInput.value = codes[categoryName] || '';
    }
}

/**
 * Update supplier code based on selection
 */
function updateSupplierCode(supplierName) {
    const supplierCodeInput = document.getElementById('supplierCode');
    if (supplierCodeInput) {
        const codes = {
            'VOLVO PARTS AB': 'VOL001',
            'CATERPILLAR INC': 'CAT001',
            'JOHN DEERE': 'JD001'
        };
        supplierCodeInput.value = codes[supplierName] || '';
    }
}

/**
 * Setup stock calculations
 */
function setupStockCalculations() {
    const stockInputs = document.querySelectorAll('.quantity-item input[type="number"]');

    stockInputs.forEach(input => {
        if (input.id !== 'totalStock' && input.id !== 'computerStock') {
            input.addEventListener('input', function() {
                calculateTotalStock();
            });
        }
    });
}

/**
 * Calculate total stock
 */
function calculateTotalStock() {
    const freeStock = parseInt(document.getElementById('freeStock')?.value || 0);
    const allocatedStock = parseInt(document.getElementById('allocatedStock')?.value || 0);
    const workshopStock = parseInt(document.getElementById('workshopStock')?.value || 0);
    const vendorStock = parseInt(document.getElementById('vendorStock')?.value || 0);

    const total = freeStock + allocatedStock + workshopStock + vendorStock;

    const totalStockInput = document.getElementById('totalStock');
    const computerStockInput = document.getElementById('computerStock');

    if (totalStockInput) totalStockInput.value = total;
    if (computerStockInput) computerStockInput.value = total;
}

/**
 * Calculate stock variance for reconciliation
 */
function calculateStockVariance() {
    const physicalCount = parseInt(document.getElementById('physicalCount')?.value || 0);
    const systemCount = parseInt(document.getElementById('systemCount')?.value || 0);
    const variance = physicalCount - systemCount;

    const varianceInput = document.getElementById('variance');
    if (varianceInput) {
        varianceInput.value = variance;

        // Color code the variance
        if (variance > 0) {
            varianceInput.style.color = '#059669'; // Green for positive
        } else if (variance < 0) {
            varianceInput.style.color = '#dc2626'; // Red for negative
        } else {
            varianceInput.style.color = '#6b7280'; // Gray for zero
        }
    }
}

/**
 * Setup stock management functionality
 */
function setupStockManagement() {
    // Reconcile stock button
    const reconcileBtn = document.getElementById('reconcileStockBtn');
    if (reconcileBtn) {
        reconcileBtn.addEventListener('click', function() {
            handleStockReconciliation();
        });
    }

    // Generate report button
    const generateReportBtn = document.getElementById('generateReportBtn');
    if (generateReportBtn) {
        generateReportBtn.addEventListener('click', function() {
            generateStockReport();
        });
    }

    // Update stock button
    const updateStockBtn = document.getElementById('updateStockBtn');
    if (updateStockBtn) {
        updateStockBtn.addEventListener('click', function() {
            updateStockLevels();
        });
    }
}

/**
 * Handle stock reconciliation
 */
function handleStockReconciliation() {
    const physicalCount = document.getElementById('physicalCount')?.value;
    const reason = document.getElementById('reconciliationReason')?.value;

    if (!physicalCount) {
        showNotification('Please enter physical count', 'error');
        return;
    }

    if (!reason && document.getElementById('variance')?.value != 0) {
        showNotification('Please provide reason for variance', 'error');
        return;
    }

    // Simulate reconciliation process
    showNotification('Processing stock reconciliation...', 'info');

    setTimeout(() => {
        showNotification('Stock reconciliation completed successfully', 'success');
        console.log('Stock reconciled:', { physicalCount, reason });
    }, 1500);
}

/**
 * Generate stock report
 */
function generateStockReport() {
    showNotification('Generating stock report...', 'info');

    setTimeout(() => {
        showNotification('Stock report generated successfully', 'success');
        console.log('Stock report generated');
    }, 1000);
}

/**
 * Update stock levels
 */
function updateStockLevels() {
    showNotification('Updating stock levels...', 'info');

    setTimeout(() => {
        showNotification('Stock levels updated successfully', 'success');
        console.log('Stock levels updated');
    }, 1000);
}

/**
 * Setup import/export functionality
 */
function setupImportExportFunctionality() {
    setupImportHandlers();
    setupExportHandlers();
}

/**
 * Setup import handlers
 */
function setupImportHandlers() {
    // Template download buttons
    const downloadPartsTemplateBtn = document.getElementById('downloadPartsTemplateBtn');
    if (downloadPartsTemplateBtn) {
        downloadPartsTemplateBtn.addEventListener('click', function() {
            downloadTemplate('parts');
        });
    }

    const downloadPricingTemplateBtn = document.getElementById('downloadPricingTemplateBtn');
    if (downloadPricingTemplateBtn) {
        downloadPricingTemplateBtn.addEventListener('click', function() {
            downloadTemplate('pricing');
        });
    }

    const downloadBinTemplateBtn = document.getElementById('downloadBinTemplateBtn');
    if (downloadBinTemplateBtn) {
        downloadBinTemplateBtn.addEventListener('click', function() {
            downloadTemplate('bin');
        });
    }

    // Upload buttons
    const uploadPartsBtn = document.getElementById('uploadPartsBtn');
    if (uploadPartsBtn) {
        uploadPartsBtn.addEventListener('click', function() {
            document.getElementById('partsFileInput')?.click();
        });
    }

    const uploadPricingBtn = document.getElementById('uploadPricingBtn');
    if (uploadPricingBtn) {
        uploadPricingBtn.addEventListener('click', function() {
            document.getElementById('pricingFileInput')?.click();
        });
    }

    const uploadBinBtn = document.getElementById('uploadBinBtn');
    if (uploadBinBtn) {
        uploadBinBtn.addEventListener('click', function() {
            document.getElementById('binFileInput')?.click();
        });
    }

    // File input handlers
    const partsFileInput = document.getElementById('partsFileInput');
    if (partsFileInput) {
        partsFileInput.addEventListener('change', function(e) {
            handleFileUpload(e.target.files[0], 'parts');
        });
    }

    const pricingFileInput = document.getElementById('pricingFileInput');
    if (pricingFileInput) {
        pricingFileInput.addEventListener('change', function(e) {
            handleFileUpload(e.target.files[0], 'pricing');
        });
    }

    const binFileInput = document.getElementById('binFileInput');
    if (binFileInput) {
        binFileInput.addEventListener('change', function(e) {
            handleFileUpload(e.target.files[0], 'bin');
        });
    }
}

/**
 * Setup export handlers
 */
function setupExportHandlers() {
    const exportPartsBtn = document.getElementById('exportPartsBtn');
    if (exportPartsBtn) {
        exportPartsBtn.addEventListener('click', function() {
            handleDataExport('parts');
        });
    }

    const exportStockBtn = document.getElementById('exportStockBtn');
    if (exportStockBtn) {
        exportStockBtn.addEventListener('click', function() {
            handleDataExport('stock');
        });
    }
}

/**
 * Download template file
 */
function downloadTemplate(type) {
    const templates = {
        'parts': 'Parts_Import_Template.xlsx',
        'pricing': 'Pricing_Update_Template.xlsx',
        'bin': 'Bin_Location_Template.xlsx'
    };

    showNotification(`Downloading ${templates[type]}...`, 'info');

    // Simulate file download
    setTimeout(() => {
        showNotification(`${templates[type]} downloaded successfully`, 'success');
        console.log(`Template downloaded: ${templates[type]}`);
    }, 500);
}

/**
 * Handle file upload
 */
function handleFileUpload(file, type) {
    if (!file) return;

    // Validate file type
    const allowedTypes = ['.xlsx', '.xls', '.csv'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
        showNotification('Please upload a valid Excel or CSV file', 'error');
        return;
    }

    // Show import status
    const importStatus = document.getElementById('importStatus');
    const progressFill = document.getElementById('progressFill');
    const statusText = document.getElementById('statusText');
    const statusDetails = document.getElementById('statusDetails');

    if (importStatus) {
        importStatus.style.display = 'block';
        statusText.textContent = `Processing ${file.name}...`;
        statusDetails.textContent = 'Validating file format and data...';

        // Simulate upload progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);

                statusText.textContent = 'Import completed successfully';
                statusDetails.textContent = `${type} data has been imported and validated.`;
                showNotification(`${file.name} imported successfully`, 'success');

                setTimeout(() => {
                    importStatus.style.display = 'none';
                    progressFill.style.width = '0%';
                }, 3000);
            }
            progressFill.style.width = progress + '%';
        }, 200);
    }

    console.log(`File uploaded: ${file.name}, Type: ${type}`);
}

/**
 * Handle data export
 */
function handleDataExport(type) {
    const exportFormat = document.getElementById('exportFormat')?.value || 'xlsx';
    const category = document.getElementById('exportCategory')?.value;
    const supplier = document.getElementById('exportSupplier')?.value;

    const filters = {
        category: category || 'All Categories',
        supplier: supplier || 'All Suppliers',
        format: exportFormat
    };

    showNotification(`Exporting ${type} data...`, 'info');

    setTimeout(() => {
        showNotification(`${type} data exported successfully as ${exportFormat.toUpperCase()}`, 'success');
        console.log(`Data exported:`, { type, filters });
    }, 1500);
}

/**
 * Setup button interactions
 */
function setupButtonInteractions() {
    const buttons = document.querySelectorAll('.btn');

    buttons.forEach(button => {
        button.addEventListener('click', function() {
            handleButtonClick(this);
        });

        // Add ripple effect
        button.addEventListener('mousedown', function(e) {
            createRippleEffect(this, e);
        });
    });
}

/**
 * Handle button click events
 */
function handleButtonClick(button) {
    const buttonText = button.textContent.trim();

    // Add loading state
    addLoadingState(button);

    // Simulate API call or action
    setTimeout(() => {
        removeLoadingState(button);
        showNotification(`${buttonText} action completed`, 'success');
    }, 1000);

    console.log(`Button clicked: ${buttonText}`);
}

/**
 * Create ripple effect on button click
 */
function createRippleEffect(button, event) {
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
    `;

    button.style.position = 'relative';
    button.style.overflow = 'hidden';
    button.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * Add loading state to button
 */
function addLoadingState(button) {
    button.disabled = true;
    button.dataset.originalText = button.textContent;
    button.textContent = 'Loading...';
    button.classList.add('loading');
}

/**
 * Remove loading state from button
 */
function removeLoadingState(button) {
    button.disabled = false;
    button.textContent = button.dataset.originalText;
    button.classList.remove('loading');
}

/**
 * Setup form interactions
 */
function setupFormInteractions() {
    const selects = document.querySelectorAll('.option-select');
    const checkboxes = document.querySelectorAll('.option-checkbox');

    selects.forEach(select => {
        select.addEventListener('change', function() {
            saveUserPreference(this.name || 'setting', this.value);
            showNotification('Setting updated', 'info');
        });
    });

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            saveUserPreference(this.name || 'setting', this.checked);
            showNotification('Setting updated', 'info');
        });
    });
}

/**
 * Update status indicators with real-time data
 */
function updateStatusIndicators() {
    const statusNumbers = document.querySelectorAll('.status-number');

    statusNumbers.forEach(element => {
        const currentValue = parseInt(element.textContent);
        const newValue = Math.floor(Math.random() * 50) + 1; // Simulate dynamic data

        animateNumber(element, currentValue, newValue, 1000);
    });
}

/**
 * Animate number changes
 */
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current;

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

/**
 * Load user preferences
 */
function loadUserPreferences() {
    const preferences = getStoredPreferences();

    Object.keys(preferences).forEach(key => {
        const element = document.querySelector(`[name="${key}"]`);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = preferences[key];
            } else {
                element.value = preferences[key];
            }
        }
    });
}

/**
 * Save user preference
 */
function saveUserPreference(key, value) {
    const preferences = getStoredPreferences();
    preferences[key] = value;
    localStorage.setItem('remanERPPreferences', JSON.stringify(preferences));
}

/**
 * Get stored preferences
 */
function getStoredPreferences() {
    const stored = localStorage.getItem('remanERPPreferences');
    return stored ? JSON.parse(stored) : {};
}

/**
 * Initialize report filters
 */
function initializeReportFilters() {
    // Placeholder for report filter functionality
    console.log('Report filters initialized');
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        background: var(--primary-teal);
        color: white;
        border-radius: 8px;
        box-shadow: var(--shadow-md);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease-in-out;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

/**
 * Setup accessibility features
 */
function setupAccessibility() {
    // Add keyboard navigation support
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });

    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });

    // Skip link removed for space optimization
}

/**
 * Utility function to debounce function calls
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Utility function to throttle function calls
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Add CSS for ripple animation
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .keyboard-navigation *:focus {
        outline: 2px solid var(--secondary-bright-blue) !important;
        outline-offset: 2px !important;
    }

    .loading {
        opacity: 0.7;
        cursor: not-allowed;
    }
`;
document.head.appendChild(style);